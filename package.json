{"name": "ai-companion-app", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "biome check", "lint:fix": "biome lint --write", "lint:fixAll": "biome check --write", "format": "biome format --write"}, "dependencies": {"@pixiv/three-vrm": "^3.4.2", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-scroll-area": "^1.2.10", "@radix-ui/react-slot": "^1.2.3", "@react-three/drei": "^10.7.4", "@react-three/fiber": "^9.3.0", "@tailwindcss/postcss": "^4.1.12", "@types/three": "^0.179.0", "@types/uuid": "^10.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "detect-gpu": "^5.0.70", "install": "^0.13.0", "lucide-react": "^0.542.0", "next": "15.5.0", "next-themes": "^0.4.6", "postcss": "^8.5.6", "react": "19.1.0", "react-dom": "19.1.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.12", "three": "^0.179.1", "uuid": "^11.1.0", "zustand": "^5.0.8"}, "devDependencies": {"@biomejs/biome": "2.2.0", "@serwist/next": "^9.1.1", "@types/node": "^20.19.11", "@types/react": "^19.1.12", "@types/react-dom": "^19.1.9", "serwist": "^9.1.1", "tw-animate-css": "^1.3.7", "typescript": "^5.9.2"}, "packageManager": "pnpm@10.15.0"}