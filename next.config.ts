import withSerwistInit from "@serwist/next";
import type { NextConfig } from "next";

const withSerwist = withSerwistInit({
  swSrc: "src/app/sw.ts",
  swDest: "public/sw.js",
  disable: process.env.NODE_ENV === "development",
});

const nextConfig: NextConfig = {
  /* config options here */
  reactStrictMode: true,
  poweredByHeader: false,
  images: {
    unoptimized: false,
  },
  compiler: {
    removeConsole:
      process.env.NODE_ENV === "production"
        ? { exclude: ["error", "warn"] }
        : false,
  },

  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
          {
            key: "X-Frame-Options",
            value: "DENY",
          },
          {
            key: "X-XSS-Protection",
            value: "1; mode=block",
          },
          {
            key: "Permissions-Policy",
            value: "camera=(), microphone=(), geolocation=()",
          },
          {
            key: "Strict-Transport-Security",
            value: "max-age=63072000; includeSubDomains; preload",
          },
          // {
          //   key: "Content-Security-Policy",
          //   value: `
          //     default-src 'self' 'unsafe-eval' 'unsafe-inline' data:;
          //     script-src 'self' 'unsafe-inline' 'unsafe-eval';
          //     style-src 'self' 'unsafe-inline';
          //     img-src 'self' data:;
          //     font-src 'self' data:;
          //     media-src 'self';
          //     object-src 'none';
          //     frame-ancestors 'none';
          //     form-action 'self';
          //   `
          //     .replace(/\s+/g, " ")
          //     .trim(),
          // },
        ],
      },
    ];
  },
};

export default withSerwist(nextConfig);
