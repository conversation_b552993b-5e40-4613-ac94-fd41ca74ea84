{"$schema": "https://biomejs.dev/schemas/2.2.0/schema.json", "vcs": {"enabled": true, "clientKind": "git", "useIgnoreFile": true}, "files": {"ignoreUnknown": false}, "formatter": {"enabled": true, "formatWithErrors": false, "indentStyle": "space", "indentWidth": 2, "lineEnding": "lf", "lineWidth": 80, "attributePosition": "auto", "bracketSameLine": false, "bracketSpacing": true, "expand": "auto", "useEditorconfig": true, "includes": ["**", "!**/node_modules", "!**/dist", "!**/build", "!**/coverage", "!**/pnpm-lock.yaml", "!**/package-lock.json", "!**/yarn.lock"]}, "linter": {"enabled": true, "rules": {"recommended": true, "complexity": {"noAdjacentSpacesInRegex": "error", "noExtraBooleanCast": "error", "noUselessCatch": "error", "noUselessEscapeInRegex": "error", "noUselessTypeConstraint": "error"}, "correctness": {"noConstAssign": "error", "noConstantCondition": "error", "noEmptyCharacterClassInRegex": "error", "noEmptyPattern": "error", "noGlobalObjectCalls": "error", "noInvalidBuiltinInstantiation": "error", "noInvalidConstructorSuper": "error", "noNonoctalDecimalEscape": "error", "noPrecisionLoss": "error", "noSelfAssign": "error", "noSetterReturn": "error", "noSwitchDeclarations": "error", "noUndeclaredVariables": "error", "noUnreachable": "error", "noUnreachableSuper": "error", "noUnsafeFinally": "error", "noUnsafeOptionalChaining": "error", "noUnusedLabels": "error", "noUnusedPrivateClassMembers": "error", "noUnusedVariables": "error", "useExhaustiveDependencies": "warn", "useHookAtTopLevel": "error", "useIsNan": "error", "useValidForDirection": "error", "useValidTypeof": "error", "useYield": "error"}, "style": {"noCommonJs": "error", "noNamespace": "error", "noParameterAssign": "warn", "useArrayLiterals": "error", "useAsConstAssertion": "error", "useBlockStatements": "off", "useConst": "error", "useTemplate": "warn"}, "suspicious": {"noUnknownAtRules": "off", "noAsyncPromiseExecutor": "error", "noCatchAssign": "error", "noClassAssign": "error", "noCompareNegZero": "error", "noConsole": {"level": "warn", "options": {"allow": ["warn", "error"]}}, "noConstantBinaryExpressions": "error", "noControlCharactersInRegex": "error", "noDebugger": "error", "noDoubleEquals": "error", "noDuplicateCase": "error", "noDuplicateClassMembers": "error", "noDuplicateElseIf": "error", "noDuplicateObjectKeys": "error", "noDuplicateParameters": "error", "noEmptyBlockStatements": "error", "noExplicitAny": "error", "noExtraNonNullAssertion": "error", "noFallthroughSwitchClause": "error", "noFunctionAssign": "error", "noGlobalAssign": "error", "noImportAssign": "error", "noIrregularWhitespace": "error", "noMisleadingCharacterClass": "error", "noMisleadingInstantiator": "error", "noPrototypeBuiltins": "error", "noRedeclare": "error", "noShadowRestrictedNames": "error", "noSparseArray": "error", "noUnsafeDeclarationMerging": "error", "noUnsafeNegation": "error", "noUselessRegexBackrefs": "error", "noWith": "error", "useAwait": "error", "useGetterReturn": "error", "useNamespaceKeyword": "error"}}, "includes": ["**", "!node_modules/", "!dist/", "!build/", "!coverage/", "!src/vite-env.d.ts", "!tsconfig*.json", "!eslint.config.*", "!*.config.*", "!*.config.*.*", "!server"]}, "javascript": {"formatter": {"jsxQuoteStyle": "double", "quoteProperties": "asNeeded", "trailingCommas": "es5", "semicolons": "always", "arrowParentheses": "always", "bracketSameLine": false, "quoteStyle": "double", "attributePosition": "auto", "bracketSpacing": true}, "globals": ["onanimationend", "ongamepadconnected", "onlostpointercapture", "onanimationiteration", "onkeyup", "onmousedown", "onanimationstart", "onslotchange", "onprogress", "ontransitionstart", "onpause", "onended", "onpointerover", "onscrollend", "onformdata", "ontransitionrun", "onanimationcancel", "ondrag", "onchange", "onbeforeinstallprompt", "onbeforexrselect", "onmessage", "ontransitioncancel", "onpointerdown", "<PERSON>ab<PERSON>", "onpointerout", "oncuechange", "ongotpointercapture", "onscrollsnapchanging", "onsearch", "onsubmit", "onstalled", "onsuspend", "onreset", "onerror", "onresize", "onmouseenter", "ongamepaddisconnected", "ondrago<PERSON>", "onbeforetoggle", "on<PERSON><PERSON>ver", "onpagehide", "<PERSON><PERSON><PERSON><PERSON>", "onratechange", "oncommand", "onmessageerror", "onwheel", "ondevicemotion", "onauxclick", "ontransitionend", "onpaste", "onpageswap", "ononline", "ondeviceorientationabsolute", "onkeydown", "onclose", "onselect", "onpageshow", "onpointercancel", "onbeforematch", "onpointerrawupdate", "ondragleave", "onscrollsnapchange", "onseeked", "onwaiting", "onbeforeunload", "onplaying", "onvolumechange", "ondragend", "onstorage", "onloadeddata", "onfocus", "onoffline", "onplay", "onafterprint", "onclick", "oncut", "onmouseout", "ondblclick", "oncanplay", "onloadstart", "onappinstalled", "onpointermove", "ontoggle", "oncontextmenu", "onblur", "oncancel", "onbeforeprint", "oncontextrestored", "onloadedmetadata", "onpointerup", "onlanguagechange", "oncopy", "onselectstart", "onscroll", "onload", "ondragstart", "onbeforeinput", "oncanplaythrough", "oninput", "oninvalid", "ontimeupdate", "ondurationchange", "onselectionchange", "onmouseup", "location", "onkeypress", "onpointer<PERSON>ve", "oncontextlost", "ondrop", "oncontentvisibilityautostatechange", "onsecuritypolicyviolation", "ondeviceorientation", "onseeking", "onrejectionhandled", "onunload", "onmouseleave", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onpointere<PERSON>", "onmousew<PERSON><PERSON>", "onunhandledrejection", "ondragenter", "onpopstate", "onpagereveal", "onemptied"]}, "html": {"formatter": {"selfCloseVoidElements": "always"}}, "overrides": [{"includes": ["**/*.ts", "**/*.tsx", "**/*.mts", "**/*.cts"], "linter": {"rules": {"complexity": {"noArguments": "error"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "noVar": "error", "noWith": "off", "useGetterReturn": "off"}}}}, {"includes": ["**/*.ts", "**/*.tsx", "**/*.mts", "**/*.cts"], "linter": {"rules": {"complexity": {"noArguments": "error"}, "correctness": {"noConstAssign": "off", "noGlobalObjectCalls": "off", "noInvalidBuiltinInstantiation": "off", "noInvalidConstructorSuper": "off", "noSetterReturn": "off", "noUndeclaredVariables": "off", "noUnreachable": "off", "noUnreachableSuper": "off"}, "style": {"useConst": "error"}, "suspicious": {"noClassAssign": "off", "noDuplicateClassMembers": "off", "noDuplicateObjectKeys": "off", "noDuplicateParameters": "off", "noFunctionAssign": "off", "noImportAssign": "off", "noRedeclare": "off", "noUnsafeNegation": "off", "noVar": "error", "noWith": "off", "useGetterReturn": "off"}}}}, {"includes": ["**/*.{ts,tsx,js,jsx}"], "linter": {"rules": {"complexity": {"useOptionalChain": "warn"}, "suspicious": {"noExplicitAny": "warn"}}}}, {"includes": ["vite.config.ts", "tsconfig.*.json"], "linter": {"rules": {}}}], "assist": {"enabled": true, "actions": {"source": {"organizeImports": "on"}}}}