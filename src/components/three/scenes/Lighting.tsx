import {
  CANVAS_CONFIG,
  type PerformanceSettings,
} from "@/components/three/utils/PerformanceSettings";

interface LightingProps {
  settings: PerformanceSettings;
}

export function Lighting({ settings }: LightingProps) {
  return (
    <>
      <ambientLight intensity={CANVAS_CONFIG.lighting.ambientIntensity} />

      <directionalLight
        position={[5, 5, 5]}
        intensity={CANVAS_CONFIG.lighting.directionalIntensity}
        castShadow={settings.shadows}
        shadow-mapSize-width={settings.shadows ? 512 : 256}
        shadow-mapSize-height={settings.shadows ? 512 : 256}
        shadow-camera-far={50}
        shadow-camera-left={-10}
        shadow-camera-right={10}
        shadow-camera-top={10}
        shadow-camera-bottom={-10}
      />
    </>
  );
}
