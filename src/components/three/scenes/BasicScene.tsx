import { AdaptiveDpr, AdaptiveEvents, OrbitControls } from "@react-three/drei";
import { CompanionModel } from "@/components/three/CompanionModel";
import { Lighting } from "@/components/three/scenes/Lighting";
import { useThreeCleanup } from "@/components/three/utils/cleanup";
import type { PerformanceSettings } from "@/components/three/utils/PerformanceSettings";

interface BasicSceneProps {
  settings: PerformanceSettings;
}

export function BasicScene({ settings }: BasicSceneProps) {
  useThreeCleanup();

  return (
    <>
      <Lighting settings={settings} />

      <CompanionModel
        vrmUrl="/models/character_01.vrm"
        scale={1.5}
        position={[0, 0, 0]}
      />

      <OrbitControls
        enablePan={true}
        enableZoom={true}
        // enableRotate={true}
        // autoRotate
        // autoRotateSpeed={0.5}
        minDistance={2}
        maxDistance={4}
        minPolarAngle={Math.PI / 6}
        maxPolarAngle={Math.PI - Math.PI / 6}
        makeDefault
      />

      <AdaptiveDpr pixelated />
      <AdaptiveEvents />
    </>
  );
}
