export interface PerformanceSettings {
  shadows: boolean;
  antialias: boolean;
  maxDpr: number;
}

export const CANVAS_CONFIG = {
  camera: {
    position: [5, 5, 5] as [number, number, number],
    fov: 50,
    near: 0.1,
    far: 1000,
  },
  orbitControls: {
    autoRotateSpeed: 0.5,
    minDistance: 2,
    maxDistance: 12,
    minPolarAngle: Math.PI / 6,
    maxPolarAngle: Math.PI - Math.PI / 6,
  },
  lighting: {
    ambientIntensity: 0.6,
    directionalIntensity: 1,
    fillIntensity: 0.3,
  },
};

export function getPerformanceSettings(tier: number): PerformanceSettings {
  switch (tier) {
    case 1: // Low-end devices
      return {
        shadows: false,
        antialias: false,
        maxDpr: 1,
      };
    case 2: // Mid-range devices
      return {
        shadows: true,
        antialias: true,
        maxDpr: 1.5,
      };
    default: // High-end devices
      return {
        shadows: true,
        antialias: true,
        maxDpr: 2,
      };
  }
}
