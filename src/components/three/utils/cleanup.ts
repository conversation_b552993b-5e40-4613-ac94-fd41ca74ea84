import { useEffect } from "react";
import { Cache } from "three";

export function useThreeCleanup() {
  useEffect(() => {
    return () => {
      Cache.clear();

      // Cleanup WebGL contexts
      const canvas = document.querySelector("canvas");

      if (canvas) {
        const gl = canvas.getContext("webgl2") || canvas.getContext("webgl");

        if (gl && "getExtension" in gl) {
          const loseContext = gl.getExtension("WEBGL_lose_context");

          if (loseContext) loseContext.loseContext();
        }
      }
    };
  }, []);
}
