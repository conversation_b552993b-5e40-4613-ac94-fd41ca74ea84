import {
  type VRM,
  VRMExpressionPresetName,
  type VRMHumanBoneName,
  VRMLoaderPlugin,
} from "@pixiv/three-vrm";
import { useFrame, useLoader } from "@react-three/fiber";
import { useEffect, useMemo, useRef, useState } from "react";
import { Box3, type Object3D, Vector3 } from "three";
import { GLTFLoader } from "three/examples/jsm/Addons.js";
import { useCompanionStore } from "@/lib/stores/useCompanionStore";

interface CompanionModelProps {
  vrmUrl?: string;
  scale?: number;
  position?: [number, number, number];
}

interface AnimationState {
  idlePhase: number;
}

export function CompanionModel({
  vrmUrl = "/models/character_01.vrm",
  scale = 1,
  position = [0, 0, 0],
}: CompanionModelProps) {
  const vrmRef = useRef<VRM | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const action = useCompanionStore((s) => s.action);

  const animationState = useMemo<AnimationState>(
    () => ({
      idlePhase: 0,
    }),
    []
  );

  const gltf = useLoader(GLTFLoader, vrmUrl, (loader) => {
    loader.register((parser) => new VRMLoaderPlugin(parser));
  });

  useEffect(() => {
    if (gltf?.userData?.vrm) {
      const vrm = gltf.userData.vrm as VRM;
      vrmRef.current = vrm;

      const box = new Box3().setFromObject(vrm.scene);
      const center = new Vector3();
      box.getCenter(center);
      vrm.scene.position.sub(center);
      vrm.scene.position.y = -box.min.y;
      vrm.scene.scale.setScalar(scale);
      vrm.scene.position.set(...position);

      if (vrm.expressionManager) {
        vrm.expressionManager.setValue(VRMExpressionPresetName.Neutral, 1.0);
      }

      //   console.info("VRM Loaded Successfully:", {
      //     humanoid: !!vrm.humanoid,
      //     expressionManager: !!vrm.expressionManager,
      //     bones: Object.keys(vrm.humanoid?.humanBones ?? {}).length,
      //   });

      setIsLoading(false);
    }
  }, [gltf, scale, position]);

  useFrame((state, delta) => {
    if (!vrmRef.current) return;

    const vrm = vrmRef.current;
    const time = state.clock.elapsedTime;

    animationState.idlePhase += delta;

    const getBone = (name: VRMHumanBoneName): Object3D | null =>
      vrm.humanoid?.getNormalizedBoneNode(name) ?? null;

    // Head
    const head = getBone("head");
    if (head) {
      head.rotation.x = Math.sin(time * 0.8) * 0.05;
      head.rotation.y = Math.sin(time * 0.6) * 0.1;
      head.rotation.z = Math.sin(time * 0.7) * 0.03;
    }

    // Chest
    const chest = getBone("chest") ?? getBone("spine");
    if (chest) {
      chest.rotation.x = Math.sin(time * 1.5) * 0.015;
      chest.rotation.y = Math.sin(time * 0.5) * 0.02;
    }

    // Arms
    const leftUpperArm = getBone("leftUpperArm");
    const rightUpperArm = getBone("rightUpperArm");
    if (leftUpperArm && rightUpperArm) {
      const armSway = Math.sin(time * 0.7) * 0.05;
      leftUpperArm.rotation.z = armSway;
      rightUpperArm.rotation.z = -armSway;
    }

    // Legs
    const leftUpperLeg = getBone("leftUpperLeg");
    const rightUpperLeg = getBone("rightUpperLeg");
    if (leftUpperLeg && rightUpperLeg) {
      const legShift = Math.sin(time * 0.3) * 0.01;
      leftUpperLeg.rotation.x = -legShift;
      rightUpperLeg.rotation.x = legShift;
    }

    vrm.update(delta);

    // Expressions
    if (vrm.expressionManager) {
      const blink = Math.sin(time * 4 + Math.PI) > 0.94 ? 1 : 0;
      vrm.expressionManager.setValue("blink", blink);

      switch (action) {
        case "wave":
          vrm.expressionManager.setValue(
            VRMExpressionPresetName.Surprised,
            1.0
          );
          break;
        case "happy":
          vrm.expressionManager.setValue(VRMExpressionPresetName.Happy, 1.0);
          break;
        default:
          vrm.expressionManager.setValue(VRMExpressionPresetName.Neutral, 1.0);
      }
    }
  });

  if (isLoading) {
    return (
      <group>
        <mesh>
          <boxGeometry args={[0.5, 1.5, 0.5]} />
          <meshStandardMaterial color="#888" wireframe />
        </mesh>
      </group>
    );
  }

  return <primitive object={gltf.userData.vrm.scene} />;
}
