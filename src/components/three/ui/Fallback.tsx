import { ShieldAlert } from "lucide-react";

export function Fallback() {
  return (
    <div className="flex h-screen items-center justify-center bg-gray-900 text-white">
      <div className="max-w-md p-8 text-center">
        <div className="mb-4 flex items-center justify-center">
          <ShieldAlert size={48} className="text-red-500" />
        </div>
        <h2 className="mb-4 text-2xl font-bold">WebGL Not Supported</h2>
        <p className="mb-4 text-gray-300">
          Your browser or device doesn't support WebGL, which is required for
          the 3D companion.
        </p>
      </div>
    </div>
  );
}
