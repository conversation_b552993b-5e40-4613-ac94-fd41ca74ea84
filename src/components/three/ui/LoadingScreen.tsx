interface LoadingScreenProps {
  tier?: number;
}

export function LoadingScreen({ tier }: LoadingScreenProps) {
  return (
    <div className="bg-opacity-90 absolute inset-0 flex items-center justify-center bg-gray-900 backdrop-blur-sm">
      <div className="text-center text-white">
        <div className="relative">
          <div className="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-b-2 border-white"></div>
        </div>
        <p className="text-lg font-medium">
          {tier ? "Optimizing for your device..." : "Detecting hardware..."}
        </p>
        <p className="mt-2 text-sm text-gray-400">
          {tier
            ? `GPU Tier: ${tier === 1 ? "Low" : tier === 2 ? "Mid" : "High"}`
            : "This may take a moment on older devices"}
        </p>
      </div>
    </div>
  );
}
