import { Check, Settings } from "lucide-react";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface PerformanceIndicatorProps {
  tier: number;
  onTierChange?: (tier: number) => void;
}

export function PerformanceIndicator({
  tier,
  onTierChange,
}: PerformanceIndicatorProps) {
  const [current, setCurrent] = useState(tier);

  const tiers = [
    { value: 1, label: "Low" },
    { value: 2, label: "Medium" },
    { value: 3, label: "High" },
  ];

  const handleSelect = (value: number) => {
    setCurrent(value);
    onTierChange?.(value);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="absolute top-4 right-4 flex items-center gap-2 "
        >
          <Settings className="h-4 w-4" />
          <span>GPU: {tiers.find((t) => t.value === current)?.label}</span>
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="end" className="w-36">
        {tiers.map(({ value, label }) => (
          <DropdownMenuItem key={value} onSelect={() => handleSelect(value)}>
            <div className="flex w-full items-center justify-between">
              {label}
              {value === current && <Check className="h-4 w-4 text-primary" />}
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
