import { Canvas } from "@react-three/fiber";
import { useEffect } from "react";
import { Cache } from "three";
import { Fallback } from "@/components/three/ui/Fallback";
import {
  CANVAS_CONFIG,
  type PerformanceSettings,
} from "@/components/three/utils/PerformanceSettings";

interface CanvasContainerProps {
  settings: PerformanceSettings;
  children: React.ReactNode;
}

export function CanvasContainer({ settings, children }: CanvasContainerProps) {
  useEffect(() => {
    return () => {
      Cache.clear();
    };
  }, []);

  return (
    <Canvas
      fallback={<Fallback />}
      shadows={settings.shadows}
      frameloop="always"
      dpr={[1, settings.maxDpr]}
      camera={CANVAS_CONFIG.camera}
      gl={{
        antialias: settings.antialias,
      }}
      style={{ touchAction: "none" }}
    >
      {children}
    </Canvas>
  );
}
