import { ShieldAlert } from "lucide-react";
import { useEffect, useState } from "react";

interface ErrorBoundaryProps {
  children: React.ReactNode;
}

export function ErrorBoundary({ children }: ErrorBoundaryProps) {
  const [hasError, setHasError] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      setError(event.error);
      setHasError(true);
    };

    const handleRejection = (event: PromiseRejectionEvent) => {
      setError(new Error(String(event.reason)));
      setHasError(true);
    };

    window.addEventListener("error", handleError);
    window.addEventListener("unhandledrejection", handleRejection);

    return () => {
      window.removeEventListener("error", handleError);
      window.removeEventListener("unhandledrejection", handleRejection);
    };
  }, []);

  if (hasError) {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-900 text-white">
        <div className="max-w-md p-8 text-center">
          <div className="mb-4 flex items-center justify-center">
            <ShieldAlert size={48} className="text-red-500" />
          </div>
          <h2 className="mb-4 text-2xl font-bold">3D Scene Error</h2>
          <p className="mb-4 text-gray-300">
            {error?.message ||
              "An unexpected error occurred while loading the 3D scene."}
          </p>
          <button
            type="button"
            onClick={() => window.location.reload()}
            className="cursor-pointer rounded bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700"
          >
            Reload Scene
          </button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}
