"use client";

import { PerformanceMonitor } from "@react-three/drei";
import { Suspense, useState } from "react";
import { CanvasContainer } from "@/components/three/core/CanvasContainer";
import { ErrorBoundary } from "@/components/three/core/ErrorBoundary";
import { BasicScene } from "@/components/three/scenes/BasicScene";
import { PerformanceIndicator } from "@/components/three/ui/PerformanceIndicator";
import GPUDetector from "@/components/three/utils/GPUDetector";
import { getPerformanceSettings } from "@/components/three/utils/PerformanceSettings";

// GPU-aware scene component
function GPUAwareScene({ detectedTier }: { detectedTier: number }) {
  const [manualTier, setManualTier] = useState<number | null>(null);
  const tier = manualTier ?? detectedTier;

  const settings = getPerformanceSettings(tier);

  return (
    <div className="w-full h-screen relative bg-gray-900">
      <CanvasContainer settings={settings}>
        <Suspense fallback={null}>
          <PerformanceMonitor
            // onIncline={() => console.log("Performance improved")}
            // onDecline={() => console.log("Performance declined")}
            factor={0.5}
          >
            <BasicScene settings={settings} />
          </PerformanceMonitor>
        </Suspense>
      </CanvasContainer>

      <PerformanceIndicator tier={tier} onTierChange={setManualTier} />
    </div>
  );
}

const CompanionScene = () => {
  return (
    <ErrorBoundary>
      <GPUDetector>
        {(detectedTier) => <GPUAwareScene detectedTier={detectedTier} />}
      </GPUDetector>
    </ErrorBoundary>
  );
};

export default CompanionScene;
