"use client";

import { forwardRef, useImperativeHandle, useRef } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import type { ChatMessage } from "@/lib/stores/useCompanionStore";

interface ChatMessageListProps {
  messages: ChatMessage[];
}

export interface ChatScrollApi {
  scrollToBottom: () => void;
}

export const ChatMessageList = forwardRef<ChatScrollApi, ChatMessageListProps>(
  ({ messages }, ref) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const viewportRef = useRef<HTMLElement | null>(null);

    useImperativeHandle(ref, () => ({
      scrollToBottom: () => {
        if (viewportRef.current) {
          viewportRef.current.scrollTo({
            top: viewportRef.current.scrollHeight,
            behavior: "smooth",
          });
        }
      },
    }));

    return (
      <div
        ref={(node) => {
          if (node) {
            containerRef.current = node;
            viewportRef.current = node.querySelector<HTMLElement>(
              "[data-radix-scroll-area-viewport]"
            );
          }
        }}
        className="flex-1 min-h-0"
      >
        <ScrollArea className="h-full">
          <div className="relative w-full p-3">
            {messages.length === 0 && (
              <div className="pt-20 text-center text-sm text-gray-500">
                Start a conversation
              </div>
            )}
            <div className="space-y-4">
              {messages.map((m) => (
                <div
                  key={m.id}
                  className={`flex items-end gap-2 ${
                    m.sender === "user" ? "justify-end" : "justify-start"
                  }`}
                >
                  {m.sender === "companion" && (
                    <div className="flex h-7 w-7 shrink-0 items-center justify-center rounded-full bg-green-600 text-xs font-bold text-white">
                      C
                    </div>
                  )}
                  <div
                    className={`max-w-[85%] min-w-0 rounded-xl px-3 py-2 text-sm leading-relaxed ${
                      m.sender === "user"
                        ? "bg-blue-600 text-white"
                        : "bg-gray-800 text-gray-100"
                    }`}
                  >
                    <p className="break-words">{m.text}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </ScrollArea>
      </div>
    );
  }
);

ChatMessageList.displayName = "ChatMessageList";
