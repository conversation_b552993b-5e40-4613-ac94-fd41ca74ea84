"use client";

import { MessageCircle, Trash2 } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { toast } from "sonner";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import {
  Sheet,
  Sheet<PERSON>ontent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { useCompanionStore } from "@/lib/stores/useCompanionStore";
import { ChatInput } from "./ChatInput";
import { ChatMessageList, type ChatScrollApi } from "./ChatMessageList";

export function Chat() {
  const [input, setInput] = useState("");
  const [mobileOpen, setMobileOpen] = useState(false);
  const [showClearDialog, setShowClearDialog] = useState(false);

  const { messages, addMessage, clearMessages } = useCompanionStore();

  const scrollApiRef = useRef<ChatScrollApi>(null);

  // biome-ignore lint/correctness/useExhaustiveDependencies: Update scroll on message change
  useEffect(() => {
    scrollApiRef.current?.scrollToBottom();
  }, [messages]);

  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768) {
        setMobileOpen(false);
      }
    };

    window.addEventListener("resize", handleResize);
    handleResize(); // Initial check

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const handleSend = () => {
    if (!input.trim()) return;
    addMessage(input.trim(), "user");
    setInput("");
    setTimeout(() => addMessage("Thinking…", "companion"), 500);
  };

  const handleClear = () => {
    clearMessages();
    toast("Chat cleared");
    setShowClearDialog(false);
  };

  const ChatBody = (
    <>
      <div className="flex items-center justify-between border-b border-gray-800 p-3">
        <h3 className="text-sm font-semibold text-gray-100">Companion</h3>
        <Button
          variant="default"
          size="sm"
          onClick={() => setShowClearDialog(true)}
          aria-label="Clear chat"
          className="cursor-pointer border border-transparent hover:border-gray-300"
          disabled={messages.length === 0}
        >
          <Trash2 className="h-4 w-4 text-gray-300" />
        </Button>
      </div>

      <ChatMessageList messages={messages} ref={scrollApiRef} />

      <ChatInput value={input} onChange={setInput} onSend={handleSend} />
    </>
  );

  return (
    <>
      {/* Mobile FAB → bottom sheet */}
      <div className="fixed right-4 bottom-4 z-40 md:hidden">
        <Sheet open={mobileOpen} onOpenChange={setMobileOpen}>
          <SheetTrigger asChild>
            <Button
              size="icon"
              className="rounded-full bg-gray-800 shadow-xl hover:bg-gray-700"
            >
              <MessageCircle className="h-6 w-6 text-gray-200" />
            </Button>
          </SheetTrigger>
          <SheetContent
            side="bottom"
            className="flex h-[75vh] flex-col rounded-t-2xl border-gray-800 bg-gray-900 p-0"
          >
            <SheetHeader className="px-4 pt-3">
              <SheetTitle className="text-gray-200">Chat</SheetTitle>
            </SheetHeader>
            <div className="flex min-h-0 flex-1 flex-col">{ChatBody}</div>
          </SheetContent>
        </Sheet>
      </div>

      {/* Desktop card */}
      <div className="hidden h-full flex-col overflow-hidden rounded-xl border border-gray-800 bg-gray-900 shadow-md md:flex">
        {ChatBody}
      </div>

      {/* Clear confirmation */}
      <AlertDialog open={showClearDialog} onOpenChange={setShowClearDialog}>
        <AlertDialogContent className="border-gray-800 bg-gray-900">
          <AlertDialogHeader>
            <AlertDialogTitle className="text-gray-100">
              Clear chat?
            </AlertDialogTitle>
            <AlertDialogDescription className="text-gray-400">
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel className="bg-gray-700 text-gray-200">
              Cancel
            </AlertDialogCancel>
            <AlertDialogAction
              onClick={handleClear}
              className="bg-red-600 text-white hover:bg-red-500"
            >
              Clear
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
