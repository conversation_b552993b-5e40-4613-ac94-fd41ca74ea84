import { create } from "zustand";
import { devtools } from "zustand/middleware";

export interface ChatMessage {
  id: string;
  text: string;
  sender: "user" | "companion";
}

interface CompanionState {
  messages: ChatMessage[];
  isSpeaking: boolean;
  addMessage: (text: string, sender: "user" | "companion") => void;
  setIsSpeaking: (speaking: boolean) => void;
  clearMessages: () => void;
}

export const useCompanionStore = create<CompanionState>()(
  devtools(
    (set) => ({
      messages: [],
      isSpeaking: false,

      addMessage: (text, sender) =>
        set((state) => ({
          messages: [
            ...state.messages,
            {
              id: crypto.randomUUID(),
              text,
              sender,
            },
          ],
        })),

      setIsSpeaking: (speaking) =>
        set(() => ({
          isSpeaking: speaking,
        })),

      clearMessages: () =>
        set(() => ({
          messages: [],
        })),
    }),
    {
      name: "companion-store",
    }
  )
);
