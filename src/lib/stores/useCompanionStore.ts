import { create } from "zustand";
import { devtools } from "zustand/middleware";

export interface ChatMessage {
  id: string;
  text: string;
  sender: "user" | "companion";
}

export type CompanionAction = "idle" | "wave" | "happy";

interface CompanionState {
  messages: ChatMessage[];
  isSpeaking: boolean;
  action: CompanionAction;
  addMessage: (text: string, sender: "user" | "companion") => void;
  setIsSpeaking: (speaking: boolean) => void;
  setAction: (action: CompanionAction) => void;
  clearMessages: () => void;
}

export const useCompanionStore = create<CompanionState>()(
  devtools(
    (set) => ({
      messages: [],
      isSpeaking: false,
      action: "idle",

      addMessage: (text, sender) =>
        set((state) => ({
          messages: [
            ...state.messages,
            {
              id: crypto.randomUUID(),
              text,
              sender,
            },
          ],
        })),

      setIsSpeaking: (speaking) =>
        set(() => ({
          isSpeaking: speaking,
        })),

      setAction: (action) => set(() => ({ action })),

      clearMessages: () =>
        set(() => ({
          messages: [],
        })),
    }),
    { name: "companion-store" }
  )
);
